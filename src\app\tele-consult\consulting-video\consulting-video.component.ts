import { Component, Input, OnInit, OnDestroy, ViewChild, Output, EventEmitter, ElementRef } from '@angular/core';
import { throwError as observableThrowError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {
  OpenviduSessionComponent,
  Session,
  UserModel,
  OpenViduLayout,
  OvSettings,
  OpenViduLayoutOptions,
  Publisher,
  OpenVidu,
  Device
} from 'openvidu-angular';
import { TeleConsultService } from '../tele-consult.service';
import * as Settings from '../../config/settings';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-consulting-video',
  templateUrl: './consulting-video.component.html',
  styleUrls: ['./consulting-video.component.css']
})
export class ConsultingVideoComponent implements OnInit, OnDestroy {
  // OpenVidu server configuration
  OPENVIDU_SERVER_URL = Settings.OPENVIDU_SERVER_URL;
  OPENVIDU_SERVER_SECRET = Settings.OPENVIDU_SECRET;

  // Session properties
  mySessionId: string;
  myUserName = 'Participant' + Math.floor(Math.random() * 100);
  tokens: string[] = [];
  deviceTokens: string[] = [];
  session = false;
  sessionJoined = false;

  // OpenVidu objects
  ovSession: Session;
  ovLocalUsers: UserModel[];
  ovLayout: OpenViduLayout;
  ovLayoutOptions: OpenViduLayoutOptions;
  ovSettings: OvSettings;

  // Component inputs/outputs
  @Input() consultationId: any;
  @Input() participantName: any;
  @Input() closeVideoSession: any;
  @Output() showJoinButton: EventEmitter<boolean> = new EventEmitter();
  @ViewChild('ovSessionComponent') public ovSessionComponent: OpenviduSessionComponent;
  @ViewChild('secondaryContainer') secondaryContainer: ElementRef;

  // Video/audio properties
  videoToken = '';
  appointmentId = '';
  videoDevices: Device[] = [];
  audioDevices: Device[] = [];

  // Secondary camera properties
  secondaryOV: OpenVidu | null = null;
  secondarySession: Session | null = null;
  secondaryPublisher: Publisher | null = null;
  secondaryToken = '';
  isSecondaryCameraAvailable = false;

  // Camera frame assignment properties
  primaryCameraDeviceId: string | null = null;
  secondaryCameraDeviceId: string | null = null;
  isPrimaryCameraLocked = false;
  isSecondaryCameraLocked = false;

  // Device monitoring properties
  private deviceMonitorInterval: NodeJS.Timeout | null = null;
  private currentSecondaryDeviceId: string | null = null;
  private lastKnownVideoDeviceCount = 0;
  private isProcessingDeviceChange = false;

  constructor(
    private httpClient: HttpClient,
    private teleConsultService: TeleConsultService,
    private notificationService: ToastrService
  ) {}

  ngOnInit() {
    const OV = new OpenVidu();
    OV.getDevices().then(devices => {
      this.videoDevices = devices.filter(device => device.kind === 'videoinput');
      this.audioDevices = devices.filter(device => device.kind === 'audioinput');
      this.lastKnownVideoDeviceCount = this.videoDevices.length; // Initialize device count

      if (this.videoDevices.length > 0 || this.audioDevices.length > 0) {
        // Disable autopublish to manually control camera selection
        this.ovSettings = {
          chat: false,
          autopublish: true, // CRITICAL: Disable autopublish to prevent automatic camera switching
          toolbarButtons: {
            audio: true,
            video: true,
            screenShare: false,
            fullscreen: true,
            layoutSpeaking: false,
            exit: false,
          }
        };
      } else {
        this.notificationService.error('No video or audio devices have been found. Please connect at least one and refresh the page.');
      }

      // Initialize camera assignments before joining sessions
      this.initializeCameraAssignments();

      this.joinSession();
      this.joinDeviceSession();

      if (this.videoDevices.length > 1) {
        this.initSecondaryCamera();
      }

      // Start device monitoring after a delay
      setTimeout(() => this.startDeviceMonitoring(), 10000);

      this.myUserName = this.participantName;
      console.log('Participant:' + this.participantName);
    });
  }

  ngOnDestroy() {
    this.stopDeviceMonitoring();

    if (this.secondaryPublisher && this.secondarySession) {
      this.secondarySession.unpublish(this.secondaryPublisher);
    }
    if (this.secondarySession) {
      this.secondarySession.disconnect();
    }
  }

  /**
   * Initialize camera assignments to prevent switching between primary and secondary frames
   */
  private initializeCameraAssignments(): void {
    console.log('🎯 Initializing camera assignments to prevent frame switching');

    if (this.videoDevices.length === 0) {
      console.warn('No video devices available for assignment');
      return;
    }

    // Assign primary camera (first device or built-in camera)
    const builtInCamera = this.findBuiltInCamera();
    if (builtInCamera) {
      this.primaryCameraDeviceId = builtInCamera.deviceId;
      console.log('📹 Primary camera assigned (built-in):', builtInCamera.label);
    } else {
      this.primaryCameraDeviceId = this.videoDevices[0].deviceId;
      console.log('📹 Primary camera assigned (first device):', this.videoDevices[0].label);
    }

    // Lock primary camera to prevent switching
    this.isPrimaryCameraLocked = true;

    // Assign secondary camera if available
    if (this.videoDevices.length > 1) {
      const usbCamera = this.findUSBCamera();
      if (usbCamera && usbCamera.deviceId !== this.primaryCameraDeviceId) {
        this.secondaryCameraDeviceId = usbCamera.deviceId;
        this.isSecondaryCameraLocked = true;
        console.log('📹 Secondary camera assigned (USB):', usbCamera.label);
      } else {
        // Find any camera that's not the primary
        const secondaryCamera = this.videoDevices.find(device =>
          device.deviceId !== this.primaryCameraDeviceId
        );
        if (secondaryCamera) {
          this.secondaryCameraDeviceId = secondaryCamera.deviceId;
          this.isSecondaryCameraLocked = true;
          console.log('📹 Secondary camera assigned (alternative):', secondaryCamera.label);
        }
      }
    }

    console.log('🔒 Camera assignments locked:', {
      primary: this.primaryCameraDeviceId,
      secondary: this.secondaryCameraDeviceId,
      primaryLocked: this.isPrimaryCameraLocked,
      secondaryLocked: this.isSecondaryCameraLocked
    });
  }

  /**
   * Find built-in/integrated camera for primary use
   */
  private findBuiltInCamera(): Device | undefined {
    const builtInKeywords = ['facetime', 'integrated', 'built-in', 'internal'];
    return this.videoDevices.find(device =>
      builtInKeywords.some(keyword => device.label.toLowerCase().includes(keyword))
    );
  }

  /**
   * Assign only secondary camera while keeping primary camera locked
   */
  private assignSecondaryCamera(): void {
    console.log('🔒 Assigning secondary camera while keeping primary locked');

    if (this.videoDevices.length > 1) {
      const usbCamera = this.findUSBCamera();
      if (usbCamera && usbCamera.deviceId !== this.primaryCameraDeviceId) {
        this.secondaryCameraDeviceId = usbCamera.deviceId;
        this.isSecondaryCameraLocked = true;
        console.log('📹 Secondary camera assigned (USB):', usbCamera.label);
      } else {
        // Find any camera that's not the primary
        const secondaryCamera = this.videoDevices.find(device =>
          device.deviceId !== this.primaryCameraDeviceId
        );
        if (secondaryCamera) {
          this.secondaryCameraDeviceId = secondaryCamera.deviceId;
          this.isSecondaryCameraLocked = true;
          console.log('📹 Secondary camera assigned (alternative):', secondaryCamera.label);
        }
      }
    }

    console.log('🔒 Secondary camera assignment complete. Primary remains locked:', this.primaryCameraDeviceId);
  }

  /**
   * Pauses device monitoring when the user clicks mute/video buttons.
   * This is the CORE FIX to prevent camera switching.
   */
  private pauseDeviceMonitoringOnUserAction(): void {
    console.log('🎤 User action (mic/video toggle) detected. Pausing device monitoring for 10 seconds.');
    this.stopDeviceMonitoring(); // Stop the interval completely
    // Restart monitoring after a safe delay
    setTimeout(() => {
      console.log('✅ Resuming device monitoring.');
      this.startDeviceMonitoring();
    }, 10000); // 10-second safe window
  }

  /**
   * Starts monitoring for physical device changes (camera connect/disconnect).
   */
  private startDeviceMonitoring(): void {
    // Ensure no existing interval is running
    this.stopDeviceMonitoring();

    this.deviceMonitorInterval = setInterval(async () => {
      if (this.isProcessingDeviceChange) {
        return; // Don't run if a change is already being processed
      }
      await this.checkDeviceChanges();
    }, 3000); // Check every 3 seconds
    console.log('Device monitoring started.');
  }

  /**
   * Stops device monitoring.
   */
  private stopDeviceMonitoring(): void {
    if (this.deviceMonitorInterval) {
      clearInterval(this.deviceMonitorInterval);
      this.deviceMonitorInterval = null;
      console.log('Device monitoring stopped.');
    }
  }

  /**
   * Checks for changes in the NUMBER of connected video devices.
   */
  private async checkDeviceChanges(): Promise<void> {
    this.isProcessingDeviceChange = true;
    try {
      const OV = new OpenVidu();
      const devices = await OV.getDevices();
      const currentVideoDevices = devices.filter(device => device.kind === 'videoinput');
      const currentVideoDeviceCount = currentVideoDevices.length;

      // CORE LOGIC: Only proceed if the number of cameras has actually changed.
      if (currentVideoDeviceCount === this.lastKnownVideoDeviceCount) {
        // console.log(`Camera count is stable at ${currentVideoDeviceCount}. Ignoring potential label/ID changes.`);
        this.isProcessingDeviceChange = false;
        return; // No change in count, so we do nothing. This prevents the bug.
      }

      console.log(`❗️ Camera count changed from ${this.lastKnownVideoDeviceCount} to ${currentVideoDeviceCount}. Processing change...`);
      this.videoDevices = currentVideoDevices; // Update the main device list

      // CRITICAL: Verify primary camera is still available and locked
      const primaryCameraStillExists = currentVideoDevices.some(d => d.deviceId === this.primaryCameraDeviceId);
      if (!primaryCameraStillExists && this.isPrimaryCameraLocked) {
        console.error('🚨 CRITICAL: Primary camera device disconnected!');
        console.log('🔄 Attempting to reassign primary camera...');

        // Update device list and reassign cameras
        this.videoDevices = currentVideoDevices;
        this.initializeCameraAssignments();

        // Note: The primary session will need to be recreated with the new camera
        console.warn('⚠️ Primary camera reassignment may require session restart');
      }

      // A camera was ADDED
      if (currentVideoDeviceCount > this.lastKnownVideoDeviceCount && !this.isSecondaryCameraAvailable) {
        console.log('🔍 New camera connected. Checking if it matches secondary assignment.');

        // Update device list but DO NOT reassign primary camera if it's locked
        this.videoDevices = currentVideoDevices;

        // Only reassign if primary camera is not locked or doesn't exist
        if (!this.isPrimaryCameraLocked || !primaryCameraStillExists) {
          this.initializeCameraAssignments();
        } else {
          // Only assign secondary camera, keep primary locked
          this.assignSecondaryCamera();
        }

        // Only initialize secondary camera if we have a valid assignment
        if (this.secondaryCameraDeviceId && this.isSecondaryCameraLocked) {
          console.log('✅ New camera matches secondary assignment. Initializing.');
          this.initSecondaryCamera();
          this.notificationService.info('Secondary camera connected and assigned.');
        } else {
          console.log('⚠️ New camera does not match secondary assignment. Ignoring.');
        }
      }
      // A camera was REMOVED
      else if (currentVideoDeviceCount < this.lastKnownVideoDeviceCount && this.isSecondaryCameraAvailable) {
        // Check if the disconnected camera was our assigned secondary one
        const secondaryDeviceStillExists = currentVideoDevices.some(d => d.deviceId === this.secondaryCameraDeviceId);
        if (!secondaryDeviceStillExists && this.secondaryCameraDeviceId === this.currentSecondaryDeviceId) {
          console.log('🔌 Assigned secondary camera disconnected.');
          await this.handleSecondaryCameraDisconnect();
          this.notificationService.info('Secondary camera disconnected.');
        }
      }

      this.lastKnownVideoDeviceCount = currentVideoDeviceCount; // Update the count for the next check
    } catch (error) {
      console.error('Error checking device changes:', error);
    } finally {
      this.isProcessingDeviceChange = false;
    }
  }

  /**
   * Initializes the secondary camera stream using locked assignment.
   */
  private initSecondaryCamera(): void {
    if (this.videoDevices.length < 2) {
      console.warn('Cannot init secondary camera, only one video device found.');
      return;
    }

    // Use the pre-assigned secondary camera device ID
    if (this.secondaryCameraDeviceId && this.isSecondaryCameraLocked) {
      console.log('🔒 Using locked secondary camera assignment:', this.secondaryCameraDeviceId);
      this.joinSecondaryCameraWithDevice(this.secondaryCameraDeviceId);
      this.isSecondaryCameraAvailable = true;
    } else {
      console.warn('No secondary camera assignment found. Cannot initialize secondary camera.');
    }
  }

  /**
   * Connects and publishes the secondary camera stream using locked assignment.
   * @param deviceId The deviceId of the camera to use.
   */
  private async joinSecondaryCameraWithDevice(deviceId: string): Promise<void> {
    try {
      // Verify this device matches our locked secondary assignment
      if (this.isSecondaryCameraLocked && deviceId !== this.secondaryCameraDeviceId) {
        console.error('🚫 Attempted to use non-assigned device for secondary camera:', {
          requested: deviceId,
          assigned: this.secondaryCameraDeviceId
        });
        return;
      }

      // Disconnect any existing secondary stream before starting a new one
      await this.handleSecondaryCameraDisconnect();

      const response = await this.teleConsultService.getDeviceVideoToken(this.consultationId).toPromise();
      this.secondaryToken = response['token'];

      this.secondaryOV = new OpenVidu();
      this.secondarySession = this.secondaryOV.initSession();

      this.secondarySession.on('streamCreated', (event: any) => {
        if (event.stream && this.secondaryContainer?.nativeElement) {
          const subscriber = this.secondarySession!.subscribe(event.stream, this.secondaryContainer.nativeElement);
          subscriber.on('videoElementCreated', (ev: any) => {
            if (ev.element) {
              this.secondaryContainer.nativeElement.appendChild(ev.element);
              console.log('📹 Secondary camera video element created and assigned to secondary frame');
            }
          });
        }
      });

      await this.secondarySession.connect(this.secondaryToken, { clientData: this.participantName + '_secondary' });

      this.secondaryPublisher = this.secondaryOV.initPublisher(undefined, { // Publish to memory first
        videoSource: deviceId,
        publishAudio: false,
        publishVideo: true,
        resolution: '640x480',
        frameRate: 30,
        mirror: false
      });

      await this.secondarySession.publish(this.secondaryPublisher);
      this.currentSecondaryDeviceId = deviceId;
      console.log('✅ Successfully published secondary camera stream with locked device:', deviceId);
      console.log('🔒 Secondary camera is locked to secondary frame only');
    } catch (error) {
      console.error('Error streaming secondary camera:', error);
      this.isSecondaryCameraAvailable = false;
      this.currentSecondaryDeviceId = null;
    }
  }

  /**
   * Disconnects and cleans up the secondary camera stream.
   */
  private async handleSecondaryCameraDisconnect(): Promise<void> {
    if (this.secondarySession) {
        if (this.secondaryPublisher) {
            await this.secondarySession.unpublish(this.secondaryPublisher);
        }
        await this.secondarySession.disconnect();
    }
    this.secondaryPublisher = null;
    this.secondarySession = null;
    this.secondaryOV = null;
    this.isSecondaryCameraAvailable = false;
    this.currentSecondaryDeviceId = null;

    if (this.secondaryContainer?.nativeElement) {
      this.secondaryContainer.nativeElement.innerHTML = '';
    }
    console.log('Secondary camera session cleaned up.');
  }

  /**
   * Finds a suitable USB/External camera from the device list.
   */
  private findUSBCamera(): Device | undefined {
    // Prioritize devices with labels indicating they are external
    const externalKeywords = ['usb', 'external', 'webcam', 'logitech', 'microsoft', 'creative', 'genius'];
    let usbCamera = this.videoDevices.find(device => externalKeywords.some(keyword => device.label.toLowerCase().includes(keyword)));

    // If not found, try to find a camera that isn't built-in
    if (!usbCamera) {
      const internalKeywords = ['facetime', 'integrated', 'built-in', 'internal'];
      usbCamera = this.videoDevices.find(device => !internalKeywords.some(keyword => device.label.toLowerCase().includes(keyword)));
    }

    // As a final fallback, if there are more than 1 cameras and no clear winner, pick the second one
    if (!usbCamera && this.videoDevices.length > 1) {
      usbCamera = this.videoDevices[1];
    }
    return usbCamera;
  }

  // --- OpenVidu Session and Token Management Methods ---

  handlerPublisherCreatedEvent(publisher: Publisher): void {
    console.log('🎬 Primary publisher event handler - verifying camera lock');

    // Verify primary camera is locked to this publisher
    if (this.isPrimaryCameraLocked && this.primaryCameraDeviceId) {
      console.log('🔒 Primary camera verified locked to primary frame:', this.primaryCameraDeviceId);
    } else {
      console.warn('⚠️ Primary camera lock verification failed');
    }

    // Listen for the stream property change event, which fires after a mute/unmute action completes.
    publisher.on('streamPropertyChanged', (event: any) => {
      if (event.changedProperty === 'audioActive' || event.changedProperty === 'videoActive') {
        console.log(`🔄 Stream property changed: ${event.changedProperty}. Triggering device monitoring pause.`);
        this.pauseDeviceMonitoringOnUserAction();
      }

      // CRITICAL: Prevent any video source changes
      if (event.changedProperty === 'videoSource') {
        console.error('🚫 BLOCKED: Attempted to change video source on primary camera');
        console.log('🔒 Primary camera must remain locked to:', this.primaryCameraDeviceId);
        // Note: We cannot prevent this change here, but we can log it for debugging
      }
    });

    // Monitor for any device changes that might affect the primary camera
    publisher.on('videoElementCreated', () => {
      console.log('📹 Primary video element created - camera locked to:', this.primaryCameraDeviceId);
    });

    // Additional monitoring for stream events
    publisher.on('streamCreated', () => {
      console.log('📡 Primary stream created with locked camera device');
    });
  }

  onToolbarButtonClicked(event: any): void {
    console.log('Toolbar button clicked:', event.type);
    if (event.type === 'audio' || event.type === 'video') {
      // Proactively pause monitoring the moment the button is clicked.
      this.pauseDeviceMonitoringOnUserAction();
    }
  }

  joinSession() {
    this.teleConsultService.getVideoToken(this.consultationId).subscribe({
      next: (data) => {
        if (data['message']) {
          this.notificationService.error(data['message']);
        } else {
          this.videoToken = data['token'];
          this.sessionJoined = true;
          this.tokens.push(this.videoToken);
          this.session = true;
        }
      },
      error: (err) => {
        console.log('ERROR getting video token:', err);
        this.notificationService.error('Could not join session. Please refresh the page.');
      }
    });
  }

  joinDeviceSession() {
    this.teleConsultService.getDeviceVideoToken(this.consultationId).subscribe({
      next: (data) => {
        this.videoToken = data['token'];
        this.sessionJoined = true;
        this.deviceTokens.push(this.videoToken);
        this.session = true;
      },
      error: (err) => console.log('ERROR getting device token:', err)
    });
  }

  handlerSessionCreatedEvent(session: Session): void {
    console.log('🎬 Primary session created - manually creating publisher with locked camera');

    session.on('sessionDisconnected', () => {
      this.session = false;
      this.tokens = [];
      this.showJoinButton.emit(false);
    });

    // Manually create publisher with locked primary camera device
    this.createPrimaryPublisher(session);
  }

  /**
   * Create primary publisher with locked camera device to prevent switching
   */
  private async createPrimaryPublisher(session: Session): Promise<void> {
    try {
      if (!this.primaryCameraDeviceId || !this.isPrimaryCameraLocked) {
        console.error('🚫 Primary camera not properly assigned. Cannot create publisher.');
        return;
      }

      console.log('🔒 Creating primary publisher with locked device:', this.primaryCameraDeviceId);

      // Create OpenVidu instance for primary publisher
      const OV = new OpenVidu();

      // Create publisher with specific camera device
      const publisher = OV.initPublisher(undefined as any, {
        videoSource: this.primaryCameraDeviceId, // CRITICAL: Lock to specific camera
        audioSource: undefined, // Use default audio
        publishAudio: true,
        publishVideo: true,
        resolution: '1280x720',
        frameRate: 30,
        insertMode: 'APPEND',
        mirror: false
      });

      // Set up publisher event listeners
      publisher.on('accessAllowed', () => {
        console.log('✅ Primary camera access allowed with locked device:', this.primaryCameraDeviceId);
      });

      publisher.on('accessDenied', () => {
        console.error('❌ Primary camera access denied for device:', this.primaryCameraDeviceId);
      });

      // Publish the stream
      await session.publish(publisher);

      console.log('✅ Primary publisher created and published with locked camera device');
      console.log('🔒 Primary camera is now locked and cannot switch to other devices');

      // Call the existing publisher created handler
      this.handlerPublisherCreatedEvent(publisher);

    } catch (error) {
      console.error('Error creating primary publisher with locked camera:', error);
    }
  }

  handlerErrorEvent(event: any): void {
    console.error('OpenVidu error event:', event);
  }

  async getToken(): Promise<string> {
    const sessionId = await this.createSession(this.consultationId);
    return this.createToken(sessionId);
  }

  createSession(sessionId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ customSessionId: sessionId });
      const options = { headers: new HttpHeaders({ Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET), 'Content-Type': 'application/json' }) };
      this.httpClient.post<any>(this.OPENVIDU_SERVER_URL + '/api/sessions', body, options).pipe(
        catchError((error) => {
          if (error.status === 409) resolve(sessionId);
          else reject(error);
          return observableThrowError(error);
        })
      ).subscribe({ next: (response) => resolve(response.id) });
    });
  }

  createToken(sessionId: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const body = JSON.stringify({ session: sessionId });
      const options = { headers: new HttpHeaders({ Authorization: 'Basic ' + btoa('OPENVIDUAPP:' + this.OPENVIDU_SERVER_SECRET), 'Content-Type': 'application/json' }) };
      this.httpClient.post<any>(this.OPENVIDU_SERVER_URL + '/api/tokens', body, options).pipe(
        catchError((error) => {
          reject(error);
          return observableThrowError(error);
        })
      ).subscribe({ next: (response) => resolve(response.token) });
    });
  }
}   